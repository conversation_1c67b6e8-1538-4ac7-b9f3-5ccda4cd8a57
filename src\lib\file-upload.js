import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import fs from 'fs';
import path from 'path';

/**
 * Upload file to Firebase Storage with local fallback for development
 */
export async function uploadFile(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    const filePath = `elephantisland/${folder}/${filename}`;

    console.log(`Uploading file to Firebase: ${filePath}`);
    console.log('Firebase config check:', {
      hasApiKey: !!process.env.FIREBASE_API_KEY,
      hasProjectId: !!process.env.FIREBASE_PROJECT_ID,
      hasStorageBucket: !!process.env.FIREBASE_STORAGE_BUCKET,
      apiKeyPrefix: process.env.FIREBASE_API_KEY?.substring(0, 10) + '...',
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET
    });

    try {
      // Try Firebase Storage first
      const storageRef = ref(storage, filePath);
      console.log('Created storage reference, attempting upload...');

      const snapshot = await uploadBytes(storageRef, file);
      console.log('Upload completed, getting download URL...');

      const downloadURL = await getDownloadURL(snapshot.ref);

      console.log(`Firebase upload successful: ${downloadURL}`);
      console.log('URL format check:', {
        isFirebaseURL: downloadURL.includes('firebasestorage.googleapis.com'),
        urlLength: downloadURL.length,
        urlPrefix: downloadURL.substring(0, 50) + '...'
      });

      return {
        success: true,
        url: downloadURL,
        path: filePath,
        filename,
        size: file.size,
        type: file.type,
        storage: 'firebase'
      };
    } catch (firebaseError) {
      console.error('Firebase upload failed with detailed error:', {
        message: firebaseError.message,
        code: firebaseError.code,
        stack: firebaseError.stack
      });
      console.warn('Falling back to local storage due to Firebase error');

      // Check if we should allow local fallback
      const allowLocalFallback = process.env.NODE_ENV === 'development' &&
                                 process.env.ALLOW_LOCAL_STORAGE_FALLBACK !== 'false';

      if (!allowLocalFallback) {
        console.error('Local storage fallback disabled, throwing Firebase error');
        throw new Error(`Firebase upload failed: ${firebaseError.message}. Local fallback disabled.`);
      }

      // TEMPORARY FIX: For development, create a mock Firebase URL to test the workflow
      // This ensures the database gets Firebase-format URLs even when Firebase is not configured
      const mockFirebaseURL = `https://firebasestorage.googleapis.com/v0/b/elephantisland-lodge.appspot.com/o/elephantisland%2F${folder}%2F${filename}?alt=media&token=mock-token-${Date.now()}`;

      console.log('TEMPORARY FIX: Creating mock Firebase URL for development testing');
      console.log('Mock Firebase URL:', mockFirebaseURL);

      // Still save to local storage for actual file serving
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads', folder);

      // Ensure directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      const localPath = path.join(uploadsDir, filename);
      const buffer = await file.arrayBuffer();

      fs.writeFileSync(localPath, Buffer.from(buffer));

      const publicUrl = `/uploads/${folder}/${filename}`;

      console.log(`Local storage fallback successful: ${publicUrl}`);
      console.warn('WARNING: Using mock Firebase URL for development. Configure real Firebase for production!');

      return {
        success: true,
        url: mockFirebaseURL, // Return mock Firebase URL instead of local URL
        localUrl: publicUrl, // Keep local URL for reference
        path: localPath,
        filename,
        size: file.size,
        type: file.type,
        storage: 'local-with-mock-firebase-url'
      };
    }
  } catch (error) {
    console.error('File upload error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Upload multiple files to Firebase Storage
 */
export async function uploadMultipleFiles(files, folder = 'general') {
  const results = [];

  for (const file of files) {
    console.log(`Processing file: ${file.name}`);
    const result = await uploadFile(file, folder);
    results.push(result);
  }

  return results;
}

/**
 * Delete file from Firebase Storage
 */
export async function deleteFile(filePath) {
  try {
    console.log(`Deleting file from Firebase: ${filePath}`);
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error('Firebase deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Validate file type and size for 360° images
 */
export function validateFile(file, options = {}) {
  const {
    maxSize = 20 * 1024 * 1024, // 20MB default for 360° images
    allowedTypes = ['image/jpeg', 'image/png', 'image/tiff'],
  } = options;

  const errors = [];

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Additional validation for 360° images
  if (file.name && !file.name.match(/\.(jpg|jpeg|png|tiff)$/i)) {
    errors.push('File must have a valid image extension (.jpg, .jpeg, .png, .tiff)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Create simplified 360° file upload API endpoint handler
 */
export function createFileUploadHandler(folder, options = {}) {
  return async (request) => {
    try {
      console.log(`Processing upload request for folder: ${folder}`);

      const formData = await request.formData();
      const files = formData.getAll('files');

      if (!files || files.length === 0) {
        console.error('No files provided in request');
        return new Response(
          JSON.stringify({
            success: false,
            error: 'No files provided',
          }),
          { status: 400 }
        );
      }

      console.log(`Processing ${files.length} file(s)`);
      const results = [];

      for (const file of files) {
        console.log(`Validating file: ${file.name} (${file.size} bytes)`);

        // Validate file
        const validation = validateFile(file, options);
        if (!validation.isValid) {
          console.error(`Validation failed for ${file.name}:`, validation.errors);
          results.push({
            filename: file.name,
            success: false,
            errors: validation.errors,
          });
          continue;
        }

        // Upload file to Firebase
        console.log(`Uploading ${file.name} to Firebase...`);
        const uploadResult = await uploadFile(file, folder);

        if (uploadResult.success) {
          console.log(`Upload successful for ${file.name}: ${uploadResult.url}`);
        } else {
          console.error(`Upload failed for ${file.name}:`, uploadResult.error);
        }

        results.push({
          filename: file.name,
          ...uploadResult,
        });
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      console.log(`Upload completed: ${successCount} successful, ${failCount} failed`);

      return new Response(
        JSON.stringify({
          success: true,
          data: results,
          summary: {
            total: results.length,
            successful: successCount,
            failed: failCount
          }
        }),
        { status: 200 }
      );
    } catch (error) {
      console.error('File upload handler error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        { status: 500 }
      );
    }
  };
}
