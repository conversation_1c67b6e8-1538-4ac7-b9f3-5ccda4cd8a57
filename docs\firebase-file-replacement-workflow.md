# Firebase File Replacement Workflow Documentation

## Overview

This document describes the Firebase file replacement workflow implemented for the 360° management system. This feature allows users to replace existing Firebase Storage files when editing 360° records while preserving all associated data (markers, camera settings, etc.).

## Workflow Description

### Context
When a user is editing an existing 360° record (not creating a new one) and uploads a new 360° image file through the admin interface.

### Detection Logic
The system checks if the current 360° record already has a Firebase Storage URL in the `url` field of the database schema.

### User Confirmation
If an existing Firebase URL is detected, a confirmation dialog appears asking: "This 360° image already has an uploaded file. Do you want to replace the existing image with the new one? This action cannot be undone."

### Workflow Steps

#### If User Confirms "Yes":
1. **Delete existing file** from Firebase Storage using the current URL
2. **Upload new 360° image** file to Firebase Storage
3. **Update the `url` property** in the 360° database record with the new Firebase Storage URL
4. **Preserve all other existing data** (markers, camera settings, etc.)

#### If User Confirms "No":
1. **Cancel the upload operation**
2. **Keep the existing file and URL unchanged**
3. **Return to the editing interface** without changes

## Technical Implementation

### Files Modified

#### 1. Enhanced Firebase Deletion Function
**File:** `src/lib/file-upload.js`

Added `deleteFileByUrl()` function to handle Firebase URL-based deletion:

```javascript
export async function deleteFileByUrl(firebaseUrl) {
  try {
    // Check if it's a Firebase URL
    if (!firebaseUrl.includes('firebasestorage.googleapis.com')) {
      return { success: false, error: 'Not a Firebase Storage URL' };
    }
    
    // Extract file path from Firebase URL
    const urlParts = firebaseUrl.split('/o/');
    const pathPart = urlParts[1].split('?')[0];
    const filePath = decodeURIComponent(pathPart);
    
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);
    
    return { success: true, deletedPath: filePath };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

#### 2. Detection Logic in 360° Form
**File:** `src/components/360s-manager/360Form.jsx`

Added Firebase file replacement detection before duplicate checking:

```javascript
// Check for Firebase file replacement when editing existing record
if (imageFile && threeSixty && threeSixty.url) {
  const isFirebaseURL = threeSixty.url.includes('firebasestorage.googleapis.com');
  
  if (isFirebaseURL) {
    setFileReplacementInfo({
      filename: imageFile.name,
      existingData: {
        ...threeSixty,
        hasExistingFile: true,
        existingUrl: threeSixty.url,
        isFirebaseUrl: true
      },
      pendingFile: imageFile,
      isFileReplacement: true // Flag to indicate this is a file replacement
    });
    setShowFileReplacementModal(true);
    return;
  }
}
```

#### 3. Enhanced File Replacement Handler
**File:** `src/components/360s-manager/360Form.jsx`

Updated `handleFileReplacementConfirm()` to include Firebase deletion:

```javascript
const handleFileReplacementConfirm = async (fileInfo) => {
  // Step 1: Delete existing Firebase file if applicable
  if (isFirebaseReplacement) {
    const deleteResult = await deleteFileByUrl(fileInfo.existingData.existingUrl);
    // Continue even if deletion fails
  }

  // Step 2: Upload the new file
  const uploadResult = await fetch('/api/upload/360s', {
    method: 'POST',
    body: formData,
  });

  // Step 3: Update database record with new URL
  const updateData = {
    url: uploadResult.data[0].url,
    originalFileName: file.name,
  };
  
  await fetch(`/api/360s/${fileInfo.existingData._id}`, {
    method: 'PATCH',
    body: JSON.stringify(updateData),
  });
};
```

#### 4. Enhanced Confirmation Modal
**File:** `src/components/360s-manager/FileReplacementModal.jsx`

Updated modal to show different messages for Firebase replacement:

```javascript
const isFirebaseReplacement = isFileReplacement && existingData.isFirebaseUrl;

// Different header text
<h3>
  {isFirebaseReplacement ? 'Replace Existing Image' : 'Replace Existing File'}
</h3>

// Different warning message
<p>
  {isFirebaseReplacement 
    ? 'This 360° record already has an uploaded image file.'
    : 'A 360° image with this name already exists.'
  }
</p>

// Different action question
<p>
  {isFirebaseReplacement 
    ? 'Do you want to replace the existing image with the new one? This action cannot be undone.'
    : 'Do you want to replace the image file while keeping all existing markers and settings?'
  }
</p>
```

### Error Handling

The workflow includes comprehensive error handling:

1. **Firebase deletion failures**: If the existing file cannot be deleted, the workflow continues with the upload
2. **Upload failures**: Proper error messages and state cleanup
3. **Database update failures**: Rollback handling and user notification
4. **URL validation**: Ensures only Firebase URLs trigger the replacement workflow

### Data Preservation

The workflow preserves all existing 360° record data:
- ✅ Marker positions and configurations
- ✅ Camera position settings
- ✅ 360° rotation settings
- ✅ Priority and metadata
- ✅ Creation timestamps
- ✅ All other custom fields

Only the `url` and `originalFileName` fields are updated.

## Testing

### Test Endpoint
**Endpoint:** `/api/test-firebase-replacement`
**Method:** POST

Tests the complete workflow including:
1. Initial file upload and database creation
2. Replacement file upload
3. Original file deletion
4. Database update with new URL
5. Data preservation verification

### Interactive Testing
**URL:** `http://localhost:3001/test-360-upload.html`

Includes "Firebase File Replacement Test" section for comprehensive testing.

### Manual Testing Steps

1. **Create 360° record with Firebase URL:**
   - Go to admin 360° management
   - Create new 360° record with image upload
   - Verify database has Firebase URL

2. **Test replacement workflow:**
   - Edit the existing 360° record
   - Upload new image file
   - Verify confirmation dialog appears with Firebase replacement message
   - Confirm replacement

3. **Verify results:**
   - Check database record has new Firebase URL
   - Verify markers and settings are preserved
   - Confirm old file is deleted from Firebase Storage

## Production Considerations

### Firebase Configuration Required
- Real Firebase project with Storage enabled
- Proper Firebase Storage rules configured
- Valid Firebase credentials in environment variables

### Storage Rules Example
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /elephantisland/{allPaths=**} {
      allow read, write: if true; // Adjust based on security needs
    }
  }
}
```

### Monitoring
- Monitor Firebase Storage usage and costs
- Log file deletion operations for audit trail
- Track replacement workflow success/failure rates

## Integration with Existing Systems

### Compatibility
- ✅ Works with existing duplicate detection system
- ✅ Compatible with existing FileReplacementModal component
- ✅ Preserves all existing 360° functionality
- ✅ Maintains backward compatibility with non-Firebase URLs

### Workflow Priority
1. **Firebase file replacement** (when editing existing record with Firebase URL)
2. **Duplicate file detection** (when filename matches different record)
3. **Normal upload** (when no conflicts detected)

## Future Enhancements

1. **Batch replacement**: Support replacing multiple 360° files at once
2. **Version history**: Keep track of replaced files for rollback capability
3. **Storage optimization**: Automatic cleanup of orphaned files
4. **Progress indicators**: Show upload/deletion progress for large files

## Troubleshooting

### Common Issues
1. **Firebase deletion fails**: Check Firebase permissions and URL format
2. **Upload fails after deletion**: Ensure Firebase configuration is correct
3. **Modal doesn't appear**: Verify record has Firebase URL format
4. **Data not preserved**: Check PATCH request includes only URL fields

### Debug Commands
```bash
# Test Firebase replacement workflow
curl -X POST http://localhost:3001/api/test-firebase-replacement

# Test Firebase configuration
curl http://localhost:3001/api/test-firebase

# Interactive testing
open http://localhost:3001/test-360-upload.html
```
