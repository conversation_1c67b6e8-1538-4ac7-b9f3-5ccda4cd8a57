export const INITIAL_EXPERIENCE_STATE = {
    showNavbar: false,
    showMenu: false,
    showPopup: false,
    showBookingPopup: false,
    showGalleryStore: false,
    showVideoGallery: false,
    showItemInfo: {},
};

export const ACTIONS_EXPERIENCE_STATE = {
    SHOW_NAVBAR: 'SHOW_NAVBAR',
    MENU_TOGGLE: 'MENU_TOGGLE',
    POPUP_TOGGLE: 'POPUP_TOGGLE',
    POPUP_BOOKING_TOGGLE: 'POPUP_BOOKING_TOGGLE',
    POPUP_STORE_TOGGLE: 'POPUP_STORE_TOGGLE',
    POPUP_VIDOE_GALLERY_TOGGLE: 'POPUP_VIDOE_GALLERY_TOGGLE',
    POPUP_ITEM_ARTICLE_TOGGLE: 'POPUP_ITEM_ARTICLE_TOGGLE',
    RESET: 'RESET',
};

export const experienceReducer = (state, action) => {
    switch (action.type) {
      case 'SHOW_NAVBAR':
        return { 
          ...state, 
          showNavbar: !state.showNavbar 
        };
      case 'MENU_TOGGLE':
        return { 
          ...state, 
          showMenu: !state.showMenu,
          showPopup:false
        };
      case 'POPUP_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup  
        };
      case 'POPUP_BOOKING_TOGGLE':
        return { 
          ...state, 
          showBookingPopup: !state.showBookingPopup,
        };
      case 'POPUP_STORE_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup,
          showGalleryStore: true,
          showVideoGallery: false,
          showItemInfo: false, 
          showMenu:false   
        };
      case 'POPUP_VIDOE_GALLERY_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup,
          showVideoGallery: true,
          showGalleryStore: false,
          showItemInfo: false,    
          showMenu:false
        };
      case 'POPUP_ITEM_ARTICLE_TOGGLE':
        return { 
          ...state, 
           showPopup: !state.showPopup,
          showItemInfo: {showItem:true,id:action.payload},
          showGalleryStore: false,
          showVideoGallery: false,  
          showMenu:false  
        };
      case 'RESET':
        return { 
          ...state, 
          showNavbar: false,
          showMenu: false,
          showPopup: false,
          showBookingPopup: false,
          showGalleryStore: false,
          showVideoGallery: false,
          showItemInfo: false, 
        };
      default:
        return state;
    }
  };