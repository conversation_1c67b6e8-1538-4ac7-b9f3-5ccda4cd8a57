import { NextResponse } from 'next/server';
import { uploadFileServer } from '@/lib/server-file-upload';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// POST /api/test-360-workflow - Test complete 360° replacement workflow
export async function POST(request) {
  try {
    console.log('Testing complete 360° replacement workflow...');
    
    // Step 1: Test Firebase upload
    console.log('Step 1: Testing Firebase upload...');
    const testContent = 'Test 360 image content for workflow test';
    const testFile = new File([testContent], 'workflow-test-360.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now()
    });
    
    const uploadResult = await uploadFileServer(testFile, '360s', 'workflow-test-360.jpg');
    console.log('Upload result:', uploadResult);
    
    if (!uploadResult.success) {
      throw new Error(`Upload failed: ${uploadResult.error}`);
    }
    
    const isFirebaseURL = uploadResult.url.includes('firebasestorage.googleapis.com');
    const isLocalURL = uploadResult.url.startsWith('/uploads/');
    
    console.log('Upload analysis:', {
      isFirebaseURL,
      isLocalURL,
      storageType: uploadResult.storage,
      url: uploadResult.url.substring(0, 100) + '...'
    });
    
    // Step 2: Test database creation
    console.log('Step 2: Testing database creation...');
    await connectDB();
    
    const testData = {
      name: 'workflow_test_360',
      url: uploadResult.url,
      originalFileName: 'workflow-test-360.jpg',
      priority: 1,
      markerList: [],
      cameraPosition: -0.0001,
      _360Rotation: -0.0001
    };
    
    const new360 = new _360Settings(testData);
    const saved360 = await new360.save();
    console.log('Database creation successful:', saved360._id);
    
    // Step 3: Test database update (replacement simulation)
    console.log('Step 3: Testing database update (replacement)...');
    const updateData = {
      url: uploadResult.url,
      originalFileName: 'workflow-test-360-updated.jpg'
    };
    
    const updated360 = await _360Settings.findByIdAndUpdate(
      saved360._id,
      { $set: updateData },
      { new: true }
    );
    
    console.log('Database update successful');
    
    // Step 4: Verify final state
    console.log('Step 4: Verifying final state...');
    const finalRecord = await _360Settings.findById(saved360._id);
    
    const finalIsFirebaseURL = finalRecord.url.includes('firebasestorage.googleapis.com');
    const finalIsLocalURL = finalRecord.url.startsWith('/uploads/');
    
    // Cleanup test record
    await _360Settings.findByIdAndDelete(saved360._id);
    console.log('Test record cleaned up');
    
    return NextResponse.json({
      success: true,
      message: 'Complete 360° workflow test completed successfully',
      results: {
        step1_upload: {
          success: uploadResult.success,
          isFirebaseURL,
          isLocalURL,
          storageType: uploadResult.storage,
          urlPreview: uploadResult.url.substring(0, 100) + '...'
        },
        step2_creation: {
          success: true,
          recordId: saved360._id.toString()
        },
        step3_update: {
          success: true,
          updatedFields: Object.keys(updateData)
        },
        step4_verification: {
          finalIsFirebaseURL,
          finalIsLocalURL,
          finalUrlPreview: finalRecord.url.substring(0, 100) + '...'
        }
      },
      analysis: {
        workflowStatus: finalIsFirebaseURL ? 'SUCCESS' : 'FAILED',
        issue: finalIsLocalURL ? 'Local URL saved instead of Firebase URL' : null,
        recommendation: finalIsFirebaseURL ? 
          'Workflow is working correctly' : 
          'Check Firebase configuration and upload process'
      }
    });
  } catch (error) {
    console.error('360° workflow test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      troubleshooting: {
        possibleCauses: [
          'Firebase configuration invalid',
          'Database connection issue',
          'Upload process failure',
          'URL format validation error'
        ],
        nextSteps: [
          'Check Firebase environment variables',
          'Verify MongoDB connection',
          'Test individual components separately',
          'Check server logs for detailed errors'
        ]
      }
    }, { status: 500 });
  }
}

// GET /api/test-360-workflow - Show test information
export async function GET(request) {
  return NextResponse.json({
    message: 'Use POST method to test complete 360° workflow',
    endpoint: '/api/test-360-workflow',
    method: 'POST',
    description: 'Tests the complete 360° replacement workflow including upload, database operations, and URL validation',
    steps: [
      '1. Upload test file to Firebase Storage',
      '2. Create database record with Firebase URL',
      '3. Update database record (simulating replacement)',
      '4. Verify final URL format in database'
    ]
  });
}
