<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>360° Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .file-input {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>360° Upload Test Page</h1>
    <p>This page helps test the 360° upload workflow and Firebase integration.</p>

    <div class="test-section">
        <h2>1. Firebase Configuration Test</h2>
        <button onclick="testFirebaseConfig()">Test Firebase Config</button>
        <div id="firebase-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Upload Workflow Test</h2>
        <button onclick="testUploadWorkflow()">Test Upload Workflow</button>
        <div id="workflow-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Manual File Upload Test</h2>
        <div class="file-input">
            <input type="file" id="fileInput" accept="image/*">
            <button onclick="uploadTestFile()">Upload File</button>
        </div>
        <div id="upload-result"></div>
    </div>

    <div class="test-section">
        <h2>4. 360° API Test</h2>
        <button onclick="test360API()">Test 360° API</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>5. 360° Fixes Test</h2>
        <button onclick="test360Fixes()">Test Firebase URL & Confirmation Dialog Fixes</button>
        <div id="fixes-result"></div>
    </div>

    <div class="test-section">
        <h2>6. Firebase File Replacement Test</h2>
        <button onclick="testFirebaseReplacement()">Test Firebase File Replacement Workflow</button>
        <div id="replacement-result"></div>
    </div>

    <script>
        async function testFirebaseConfig() {
            const resultDiv = document.getElementById('firebase-result');
            resultDiv.innerHTML = '<p>Testing Firebase configuration...</p>';
            
            try {
                const response = await fetch('/api/test-firebase');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Firebase Configuration Test Passed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Firebase Configuration Test Failed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Firebase Test Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testUploadWorkflow() {
            const resultDiv = document.getElementById('workflow-result');
            resultDiv.innerHTML = '<p>Testing complete upload workflow...</p>';
            
            try {
                const response = await fetch('/api/test-360-workflow', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    const status = result.analysis.workflowStatus === 'SUCCESS' ? 'success' : 'warning';
                    resultDiv.innerHTML = `
                        <div class="${status}">
                            <h3>${result.analysis.workflowStatus === 'SUCCESS' ? '✅' : '⚠️'} Workflow Test Result</h3>
                            <p><strong>Status:</strong> ${result.analysis.workflowStatus}</p>
                            <p><strong>Recommendation:</strong> ${result.analysis.recommendation}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Workflow Test Failed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Workflow Test Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function uploadTestFile() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('upload-result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">Please select a file first</div>';
                return;
            }
            
            resultDiv.innerHTML = '<p>Uploading file...</p>';
            
            try {
                const formData = new FormData();
                formData.append('files', fileInput.files[0]);
                
                const response = await fetch('/api/upload/360s', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const isFirebaseURL = result.data[0].url.includes('firebasestorage.googleapis.com');
                    const status = isFirebaseURL ? 'success' : 'warning';
                    
                    resultDiv.innerHTML = `
                        <div class="${status}">
                            <h3>${isFirebaseURL ? '✅' : '⚠️'} File Upload Result</h3>
                            <p><strong>URL Type:</strong> ${isFirebaseURL ? 'Firebase URL' : 'Local URL'}</p>
                            <p><strong>URL:</strong> ${result.data[0].url}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Upload Failed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Upload Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function test360API() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>Testing 360° API...</p>';

            try {
                const response = await fetch('/api/360s?limit=5');
                const result = await response.json();

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 360° API Test Passed</h3>
                            <p><strong>Records found:</strong> ${result.data.length}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ 360° API Test Failed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ API Test Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function test360Fixes() {
            const resultDiv = document.getElementById('fixes-result');
            resultDiv.innerHTML = '<p>Testing 360° fixes...</p>';

            try {
                const response = await fetch('/api/test-360-fixes', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    const overallStatus = result.overallStatus;
                    const allWorking = Object.values(overallStatus).every(status => status === 'WORKING');
                    const statusClass = allWorking ? 'success' : 'warning';

                    resultDiv.innerHTML = `
                        <div class="${statusClass}">
                            <h3>${allWorking ? '✅' : '⚠️'} 360° Fixes Test Results</h3>
                            <div style="margin: 10px 0;">
                                <p><strong>Firebase URL Fix:</strong> ${overallStatus.firebaseUrlFix}</p>
                                <p><strong>Confirmation Dialog Fix:</strong> ${overallStatus.confirmationDialogFix}</p>
                                <p><strong>Database Saving Fix:</strong> ${overallStatus.databaseSavingFix}</p>
                            </div>
                            <div style="margin: 10px 0;">
                                <h4>Recommendations:</h4>
                                <p>${result.recommendations.firebaseUrl}</p>
                                <p>${result.recommendations.confirmationDialog}</p>
                            </div>
                            <details>
                                <summary>View Detailed Results</summary>
                                <pre>${JSON.stringify(result, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ 360° Fixes Test Failed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Fixes Test Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testFirebaseReplacement() {
            const resultDiv = document.getElementById('replacement-result');
            resultDiv.innerHTML = '<p>Testing Firebase file replacement workflow...</p>';

            try {
                const response = await fetch('/api/test-firebase-replacement', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    const status = result.workflowSuccess ? 'success' : 'warning';
                    const analysis = result.analysis;

                    resultDiv.innerHTML = `
                        <div class="${status}">
                            <h3>${result.workflowSuccess ? '✅' : '⚠️'} Firebase Replacement Test Results</h3>
                            <div style="margin: 10px 0;">
                                <p><strong>Overall Status:</strong> ${analysis.overallStatus}</p>
                                <p><strong>Firebase Upload:</strong> ${analysis.firebaseUpload}</p>
                                <p><strong>File Replacement:</strong> ${analysis.fileReplacement}</p>
                                <p><strong>Data Preservation:</strong> ${analysis.dataPreservation}</p>
                                <p><strong>File Deletion:</strong> ${analysis.fileDeletion}</p>
                            </div>
                            <div style="margin: 10px 0;">
                                <h4>Recommendations:</h4>
                                ${result.recommendations.map(rec => `<p>• ${rec}</p>`).join('')}
                            </div>
                            <details>
                                <summary>View Detailed Test Results</summary>
                                <pre>${JSON.stringify(result, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Firebase Replacement Test Failed</h3>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Replacement Test Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
