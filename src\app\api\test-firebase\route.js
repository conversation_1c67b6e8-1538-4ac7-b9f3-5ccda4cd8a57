import { NextResponse } from 'next/server';
import { storage } from '@/lib/firebase';
import { ref, listAll } from 'firebase/storage';

// GET /api/test-firebase - Test Firebase Storage configuration
export async function GET(request) {
  try {
    console.log('Testing Firebase Storage configuration...');
    
    // Check environment variables
    const firebaseConfig = {
      apiKey: process.env.FIREBASE_API_KEY ? '***SET***' : 'NOT SET',
      authDomain: process.env.FIREBASE_AUTH_DOMAIN ? '***SET***' : 'NOT SET',
      projectId: process.env.FIREBASE_PROJECT_ID ? '***SET***' : 'NOT SET',
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET ? '***SET***' : 'NOT SET',
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID ? '***SET***' : 'NOT SET',
      appId: process.env.FIREBASE_APP_ID ? '***SET***' : 'NOT SET',
    };
    
    console.log('Firebase config status:', firebaseConfig);
    
    // Test storage access
    const storageRef = ref(storage, 'elephantisland/');
    const result = await listAll(storageRef);
    
    console.log('Firebase Storage test successful');
    
    return NextResponse.json({
      success: true,
      message: 'Firebase Storage is working correctly',
      config: firebaseConfig,
      itemsFound: result.items.length,
      foldersFound: result.prefixes.length
    });
  } catch (error) {
    console.error('Firebase Storage test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      code: error.code,
      config: {
        apiKey: process.env.FIREBASE_API_KEY ? '***SET***' : 'NOT SET',
        authDomain: process.env.FIREBASE_AUTH_DOMAIN ? '***SET***' : 'NOT SET',
        projectId: process.env.FIREBASE_PROJECT_ID ? '***SET***' : 'NOT SET',
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET ? '***SET***' : 'NOT SET',
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID ? '***SET***' : 'NOT SET',
        appId: process.env.FIREBASE_APP_ID ? '***SET***' : 'NOT SET',
      }
    }, { status: 500 });
  }
}
