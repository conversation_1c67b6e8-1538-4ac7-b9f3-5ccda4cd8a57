'use client'
import Link from 'next/link';
import { useSearchParams } from 'next/navigation'
import React from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive';
import _360BtnMenu from './_360BtnMenu'
import _360BookNowBtn from './_360BookNowBtn'
import { useContextExperience } from '@/contexts/useContextExperience';

export default function Navbar() {
    const searchParams = useSearchParams()
    const { experienceState } = useContextExperience()
    const id = searchParams.get('id')
    const links=['the island','experiences','dining','testimonials','location & contacts']

    // console.log('_360Navbar:',experienceState)

  return (
    (id !== 'entrance_360' &&<div className='flex absolute z-50 top-0 left-0 w-full h-fit items-center bg-black/50'>
      <div className='flex w-full h-fit items-center justify-between'>
        <div href={'/'} className="flex bg-inherit object-left-top relative w-fit h-full text-lg tracking-[6px]">
          <Link href={'/'} className="flex bg-inherit object-left-top relative w-fit h-full text-lg tracking-[6px]">
            <ImageWrapperResponsive className={'w-auto h-full'} src={'/assets/elephant_island_logo_white_for_nav_bar.png'} alt='elephant island logo'/>
          </Link>
          <div className='flex ml-10 gap-5 items-center text-sm uppercase text-white'>
            {links.map(i=><Link key={i} href={`/${i}`}>{i}</Link>)}
          </div>
        </div>
        <div className='flex items-center w-fit h-full'>
          {false && <_360BtnMenu/>}
          <_360BookNowBtn/>
        </div>
      </div>
    </div>)
  )
}
