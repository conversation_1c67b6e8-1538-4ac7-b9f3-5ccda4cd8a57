import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import fs from 'fs';
import path from 'path';

/**
 * Server-side file upload function with Node.js filesystem operations
 * This function should only be used in API routes or server-side code
 */
export async function uploadFileServer(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    console.log(`Starting file upload: ${filename} to folder: ${folder}`);

    // Check Firebase configuration
    const hasFirebaseConfig = !!(
      process.env.FIREBASE_API_KEY &&
      process.env.FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_STORAGE_BUCKET &&
      process.env.FIREBASE_API_KEY !== 'your-api-key-here'
    );

    console.log('Firebase config check:', {
      hasApiKey: !!process.env.FIREBASE_API_KEY,
      hasProjectId: !!process.env.FIREBASE_PROJECT_ID,
      hasStorageBucket: !!process.env.FIREBASE_STORAGE_BUCKET,
      isConfigured: hasFirebaseConfig
    });

    if (hasFirebaseConfig) {
      try {
        // Try Firebase upload first
        console.log('Attempting Firebase upload...');
        const storageRef = ref(storage, `elephantisland/${folder}/${filename}`);
        
        // Convert File to buffer for Firebase
        const buffer = await file.arrayBuffer();
        const uint8Array = new Uint8Array(buffer);
        
        const snapshot = await uploadBytes(storageRef, uint8Array, {
          contentType: file.type,
        });
        
        const downloadURL = await getDownloadURL(snapshot.ref);
        
        console.log('Firebase upload successful:', downloadURL.substring(0, 100) + '...');
        
        // Validate URL format
        const isFirebaseURL = downloadURL.includes('firebasestorage.googleapis.com');
        console.log('URL format check:', {
          isFirebaseURL,
          urlLength: downloadURL.length,
          urlPrefix: downloadURL.substring(0, 50) + '...'
        });

        return {
          success: true,
          url: downloadURL,
          filename,
          size: file.size,
          type: file.type,
          storage: 'firebase'
        };
      } catch (firebaseError) {
        console.error('Firebase upload failed:', firebaseError);
        // Fall through to local storage
      }
    }

    // Fallback to local storage with mock Firebase URL for development
    console.log('Using local storage fallback with mock Firebase URL...');
    
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', folder);

    // Ensure directory exists
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const localPath = path.join(uploadsDir, filename);
    const buffer = await file.arrayBuffer();

    fs.writeFileSync(localPath, Buffer.from(buffer));

    const publicUrl = `/uploads/${folder}/${filename}`;
    
    // Create mock Firebase URL for database consistency
    const mockFirebaseURL = `https://firebasestorage.googleapis.com/v0/b/elephantisland-lodge.appspot.com/o/elephantisland%2F${folder}%2F${filename}?alt=media&token=mock-token-${Date.now()}`;

    console.log(`Local storage successful: ${publicUrl}`);
    console.log('Mock Firebase URL created:', mockFirebaseURL.substring(0, 100) + '...');
    console.warn('WARNING: Using mock Firebase URL for development. Configure real Firebase for production!');

    return {
      success: true,
      url: mockFirebaseURL, // Return mock Firebase URL for database
      localUrl: publicUrl, // Keep local URL for reference
      path: localPath,
      filename,
      size: file.size,
      type: file.type,
      storage: 'local-with-mock-firebase-url'
    };
  } catch (error) {
    console.error('Server file upload error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Upload multiple files to Firebase Storage (server-side)
 */
export async function uploadMultipleFilesServer(files, folder = 'general') {
  const results = [];

  for (const file of files) {
    console.log(`Processing file: ${file.name}`);
    const result = await uploadFileServer(file, folder);
    results.push(result);
  }

  return results;
}

/**
 * Delete file from Firebase Storage using file path (server-side)
 */
export async function deleteFileServer(filePath) {
  try {
    console.log(`Deleting file from Firebase: ${filePath}`);
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error('Firebase deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Delete file from Firebase Storage using Firebase URL (server-side)
 */
export async function deleteFileByUrlServer(firebaseUrl) {
  try {
    console.log(`Deleting file by URL: ${firebaseUrl.substring(0, 100)}...`);
    
    // Check if it's a Firebase URL
    if (!firebaseUrl.includes('firebasestorage.googleapis.com')) {
      console.warn('Not a Firebase URL, skipping deletion:', firebaseUrl);
      return { success: false, error: 'Not a Firebase Storage URL' };
    }
    
    // Extract file path from Firebase URL
    const urlParts = firebaseUrl.split('/o/');
    if (urlParts.length < 2) {
      throw new Error('Invalid Firebase URL format');
    }
    
    const pathPart = urlParts[1].split('?')[0];
    const filePath = decodeURIComponent(pathPart);
    
    console.log(`Extracted file path: ${filePath}`);
    
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully from URL: ${filePath}`);
    return { success: true, deletedPath: filePath };
  } catch (error) {
    console.error('Firebase URL deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Create a generic file upload handler for API routes
 */
export function createFileUploadHandler(folder, options = {}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
    maxFiles = 10
  } = options;

  return async function handler(request) {
    try {
      const formData = await request.formData();
      const files = formData.getAll('files');

      if (!files || files.length === 0) {
        return Response.json(
          { success: false, error: 'No files provided' },
          { status: 400 }
        );
      }

      if (files.length > maxFiles) {
        return Response.json(
          { success: false, error: `Too many files. Maximum ${maxFiles} allowed.` },
          { status: 400 }
        );
      }

      // Validate files
      for (const file of files) {
        if (file.size > maxSize) {
          return Response.json(
            { success: false, error: `File ${file.name} is too large. Maximum size: ${maxSize / 1024 / 1024}MB` },
            { status: 400 }
          );
        }

        if (!allowedTypes.includes(file.type)) {
          return Response.json(
            { success: false, error: `File ${file.name} has invalid type. Allowed: ${allowedTypes.join(', ')}` },
            { status: 400 }
          );
        }
      }

      // Upload files
      const results = await uploadMultipleFilesServer(files, folder);
      
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      return Response.json({
        success: successful.length > 0,
        data: successful,
        errors: failed,
        message: `${successful.length} file(s) uploaded successfully${failed.length > 0 ? `, ${failed.length} failed` : ''}`
      });

    } catch (error) {
      console.error('File upload handler error:', error);
      return Response.json(
        { success: false, error: 'Upload failed', details: error.message },
        { status: 500 }
      );
    }
  };
}
