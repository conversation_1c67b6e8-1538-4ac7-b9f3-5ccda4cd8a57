'use client';

import { useState } from 'react';
import { Md<PERSON><PERSON>ning, MdClose, MdImage, MdLocationOn, MdCameraAlt, MdSchedule } from 'react-icons/md';

export default function FileReplacementModal({ 
  isOpen, 
  onClose, 
  fileInfo, 
  onConfirm, 
  onCancel 
}) {
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen || !fileInfo) return null;

  const { filename, existingData } = fileInfo;

  // Safety check for existingData
  if (!existingData) {
    console.error('FileReplacementModal: existingData is missing', fileInfo);
    return null;
  }

  const handleConfirm = async () => {
    setIsProcessing(true);
    try {
      await onConfirm(fileInfo);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancel = () => {
    onCancel(fileInfo);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Unknown';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <MdWarning className="h-6 w-6 text-amber-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900">
              Replace Existing File
            </h3>
          </div>
          <button
            onClick={onClose}
            disabled={isProcessing}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <MdClose className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* File Info */}
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
            <div className="flex items-start space-x-3">
              <MdImage className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-amber-800">
                  File: <span className="font-mono">{filename}</span>
                </p>
                <p className="text-sm text-amber-700 mt-1">
                  A 360° image with this name already exists.
                </p>
              </div>
            </div>
          </div>

          {/* Existing File Details */}
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Existing File Details:</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <MdImage className="h-4 w-4 text-gray-400" />
                <span>Original: {existingData?.originalFileName || 'Unknown'}</span>
              </div>
              
              {existingData?.hasMarkers && (
                <div className="flex items-center space-x-2">
                  <MdLocationOn className="h-4 w-4 text-blue-500" />
                  <span className="text-blue-600 font-medium">Contains marker data</span>
                </div>
              )}
              
              {existingData?.hasCameraSettings && (
                <div className="flex items-center space-x-2">
                  <MdCameraAlt className="h-4 w-4 text-green-500" />
                  <span className="text-green-600 font-medium">Has camera settings</span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <MdSchedule className="h-4 w-4 text-gray-400" />
                <span>Created: {formatDate(existingData?.createdAt)}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-gray-200 px-2 py-1 rounded">
                  Priority: {existingData?.priority || 0}
                </span>
              </div>
            </div>
          </div>

          {/* Data Preservation Warning */}
          {(existingData?.hasMarkers || existingData?.hasCameraSettings) && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex items-start space-x-3">
                <MdLocationOn className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">All data will be preserved</p>
                  <p className="mt-1">
                    If you replace this file, all existing marker positions and camera settings will be kept with the new image.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Question */}
          <div className="pt-2">
            <p className="text-sm text-gray-700 font-medium">
              Do you want to replace the image file while keeping all existing markers and settings?
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-gray-200">
          <button
            onClick={handleCancel}
            disabled={isProcessing}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            No, Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={isProcessing}
            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? 'Replacing...' : 'Yes, Replace File'}
          </button>
        </div>
      </div>
    </div>
  );
}
