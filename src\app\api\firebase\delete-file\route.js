import { NextResponse } from 'next/server';
import { deleteFileByUrlServer } from '@/lib/server-file-upload';

// POST /api/firebase/delete-file - Delete file from Firebase Storage by URL
export async function POST(request) {
  try {
    const body = await request.json();
    const { url } = body;
    
    if (!url) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'URL is required',
        },
        { status: 400 }
      );
    }
    
    console.log('Deleting Firebase file via API:', url.substring(0, 100) + '...');
    
    const result = await deleteFileByUrlServer(url);

    if (result.success) {
      console.log('Firebase file deletion successful');
      return NextResponse.json({
        success: true,
        message: 'File deleted successfully',
        deletedPath: result.deletedPath
      });
    } else {
      // Check if it's a "file not found" error - this is acceptable for mock URLs
      const isFileNotFound = result.error && result.error.includes('does not exist');
      const isMockUrl = url.includes('mock-token');

      if (isFileNotFound && isMockUrl) {
        console.log('Mock Firebase URL detected - skipping deletion (this is expected in development)');
        return NextResponse.json({
          success: true,
          message: 'Mock Firebase URL - deletion skipped (development mode)',
          skipped: true
        });
      }

      console.warn('Firebase file deletion failed:', result.error);
      return NextResponse.json({
        success: false,
        error: 'Deletion Failed',
        message: result.error,
        isFileNotFound,
        isMockUrl
      }, { status: 400 });
    }
    
  } catch (error) {
    console.error('Firebase deletion API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// GET /api/firebase/delete-file - Show API information
export async function GET(request) {
  return NextResponse.json({
    message: 'Use POST method to delete Firebase files',
    endpoint: '/api/firebase/delete-file',
    method: 'POST',
    body: {
      url: 'Firebase Storage URL to delete'
    },
    description: 'Deletes a file from Firebase Storage using its download URL'
  });
}
