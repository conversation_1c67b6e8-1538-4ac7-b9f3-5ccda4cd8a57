'use client';

import { useRef, useEffect, useState, useMemo, useCallback, memo } from 'react';
import { OrbitControls } from '@react-three/drei';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { loadImageWithCache } from '@/lib/asset-loader';
import { degToRad } from 'three/src/math/MathUtils';

// Memoized OrbitControls component to prevent unnecessary re-renders
const MemoizedOrbitControls = memo(OrbitControls);

function PanoramicSphere({
  currentImage,// This prop holds the camera position and rotation data
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad,
}) {
  const meshRef = useRef();
  const controlsRef = useRef();
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [cameraInitialized, setCameraInitialized] = useState(false);

  const { camera } = useThree();

  // Memoized geometry and material for better performance
  const sphereGeometry = useMemo(() => {
    const geometry = new THREE.SphereGeometry(32, 60, 40);
    // Dispose of geometry on unmount
    return geometry;
  }, []);

  const basicMaterial = useMemo(() => {
    const material = new THREE.MeshBasicMaterial({
      side: THREE.BackSide,
      transparent: false,
    });
    return material;
  }, []);

  // Memoized OrbitControls configuration
  const controlsConfig = useMemo(() => ({
    enableZoom: false,
    enablePan: false,
    enableRotate: true,
    enableDamping: true,
    dampingFactor: 0.05,
    rotateSpeed: -0.35,
    maxDistance:0.5,
    minDistance:0,
    minPolarAngle: degToRad(60),
    maxPolarAngle: degToRad(120),
    minAzimuthAngle: -Infinity,
    maxAzimuthAngle: Infinity,
  }), []);

  // Cleanup geometries and materials on unmount
  useEffect(() => {
    return () => {
      sphereGeometry?.dispose();
      basicMaterial?.dispose();
      currentTexture?.dispose();
    };
  }, [sphereGeometry, basicMaterial, currentTexture]);

  // Optimized texture configuration function
  const configureTexture = useCallback((texture) => {
    texture.mapping = THREE.EquirectangularReflectionMapping;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.flipY = true;
    // Set proper color space for accurate color representation
    texture.colorSpace = THREE.SRGBColorSpace;
    texture.needsUpdate = true;
    return texture;
  }, []);

  // Enhanced texture loading with production-ready error handling and URL validation
  const loadTexture = useCallback(async (url, id) => {
    // Validate URL before attempting to load
    if (!url || typeof url !== 'string') {
      console.error(`PanoramicSphere - Invalid URL for ID ${id}:`, url);
      return null;
    }

    // Check if we have a cached texture for this ID
    if (textureCache.has(id)) {
      const cachedEntry = textureCache.get(id);

      // If cached entry has URL validation and URLs match, return cached texture
      if (cachedEntry && cachedEntry.url === url) {
        return cachedEntry.texture;
      }

      // If URLs don't match, invalidate the cache entry
      if (cachedEntry && cachedEntry.url !== url) {
        console.log(`Cache invalidation: URL changed for ID ${id}`);
        setTextureCache(prevCache => {
          const newCache = new Map(prevCache);
          newCache.delete(id);
          return newCache;
        });
      }
    }

    setIsLoading(true);

    try {
      // Validate and normalize URL for production
      const normalizedUrl = normalizeImageUrl(url);

      // Use Three.js TextureLoader with enhanced production settings
      const textureLoader = new THREE.TextureLoader();

      // Enhanced cross-origin settings for production
      textureLoader.setCrossOrigin('anonymous');

      const texture = await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error(`Texture loading timeout for ID ${id}`));
        }, 20000); // Increased timeout for production

        // Add progress tracking for better debugging
        textureLoader.load(
          normalizedUrl,
          (loadedTexture) => {
            clearTimeout(timeoutId);
            configureTexture(loadedTexture);
            console.log(`Successfully loaded texture for ID ${id}`);
            resolve(loadedTexture);
          },
          (progress) => {
            // Optional: Log progress for debugging
            if (progress.lengthComputable) {
              const percentComplete = (progress.loaded / progress.total) * 100;
              console.log(`Loading texture ${id}: ${percentComplete.toFixed(1)}%`);
            }
          },
          (error) => {
            clearTimeout(timeoutId);
            console.error(`Failed to load texture for ID ${id} from URL ${normalizedUrl}:`, error);
            reject(error);
          }
        );
      });

      // Cache the loaded texture with URL for validation
      setTextureCache(prevCache => {
        const newCache = new Map(prevCache);
        newCache.set(id, { texture, url: normalizedUrl });
        return newCache;
      });

      return texture;
    } catch (error) {
      console.error(`PanoramicSphere - Error loading texture for ID ${id}:`, error);

      // Try fallback URL if available
      const fallbackUrl = getFallbackImageUrl(id);
      if (fallbackUrl && fallbackUrl !== url) {
        console.log(`Attempting fallback URL for ID ${id}: ${fallbackUrl}`);
        try {
          return await loadTexture(fallbackUrl, id);
        } catch (fallbackError) {
          console.error(`Fallback also failed for ID ${id}:`, fallbackError);
        }
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  }, [textureCache, setTextureCache, configureTexture, setIsLoading]);

  // URL normalization for production compatibility
  const normalizeImageUrl = useCallback((url) => {
    if (!url) return null;

    // If it's already a full URL (Firebase, etc.), return as-is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If it's a relative path, ensure it starts with /
    if (!url.startsWith('/')) {
      return `/${url}`;
    }

    return url;
  }, []);

  // Fallback image URL generation
  const getFallbackImageUrl = useCallback((id) => {
    // Try common fallback patterns
    const fallbacks = [
      `/uploads/360s/${id}.jpg`,
      `/uploads/360s/${id}.png`,
      `/assets/360s/${id}.jpg`,
      `/assets/360s/default-360.jpg`
    ];

    // Return first fallback for now - in a real implementation,
    // you might want to check if these files exist
    return fallbacks[0];
  }, []);

  // Optimized background texture loading with better performance
  useEffect(() => {
    if (loadingQueue.length === 0) return;

    let isCancelled = false;

    const loadNextTexture = async () => {
      if (isCancelled) return;

      const nextItem = loadingQueue[0];
      if (!nextItem) return;

      // Check if texture is cached and URL matches
      if (textureCache.has(nextItem._id)) {
        const cachedEntry = textureCache.get(nextItem._id);
        if (cachedEntry && cachedEntry.url === nextItem.url) {
          setLoadingQueue(prev => prev.slice(1));
          return;
        }
      }

      try {
        // Update status to downloading
        setLoadingQueue(prev =>
          prev.map(item =>
            item._id === nextItem._id
              ? { ...item, status: 'downloading' }
              : item
          )
        );

        // Load texture with cancellation check
        if (!isCancelled) {
          await loadTexture(nextItem.url, nextItem._id);
        }

        // Remove from queue if not cancelled
        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      } catch (error) {
        console.error(`Background texture loading failed for ID ${nextItem._id}:`, error);
        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      }
    };

    // Use requestIdleCallback for better performance if available
    const scheduleLoad = () => {
      if (typeof window !== 'undefined' && window.requestIdleCallback) {
        window.requestIdleCallback(loadNextTexture, { timeout: 1000 });
      } else {
        setTimeout(loadNextTexture, 100);
      }
    };

    scheduleLoad();

    return () => {
      isCancelled = true;
    };
  }, [loadingQueue, textureCache, setLoadingQueue, loadTexture]);

  // Optimized current texture loading with better state management
  useEffect(() => {
    if (!imageUrl || !imageId) {
      setCurrentTexture(null);
      return;
    }

    let isCancelled = false;

    const loadCurrentTexture = async () => {
      try {
        const texture = await loadTexture(imageUrl, imageId);
        if (texture && !isCancelled) {
          setCurrentTexture(texture);
          onTextureLoad?.();
        } else if (!isCancelled) {
          setCurrentTexture(null);
        }
      } catch (error) {
        console.error('Error loading current texture:', error);
        if (!isCancelled) {
          setCurrentTexture(null);
        }
      }
    };

    loadCurrentTexture();

    return () => {
      isCancelled = true;
    };
  }, [imageUrl, imageId, loadTexture, onTextureLoad]);

  // Update material when texture changes
  useEffect(() => {
    if (basicMaterial && currentTexture) {
      basicMaterial.map = currentTexture;
      basicMaterial.needsUpdate = true;
    } else if (basicMaterial) {
      basicMaterial.map = null;
      basicMaterial.needsUpdate = true;
    }
  }, [basicMaterial, currentTexture]);

  // Initialize camera position and rotation from stored database values
  useEffect(() => {
    if (!currentImage || !controlsRef.current || !meshRef.current || cameraInitialized) {
      return;
    }

    // Apply stored camera position (vertical offset)
    if (typeof currentImage.cameraPosition === 'number') {
      controlsRef.current.target.y = currentImage.cameraPosition;
      controlsRef.current.update();
    }

    // Apply stored 360 rotation to mesh
    if (typeof currentImage._360Rotation === 'number') {
      meshRef.current.rotation.y = currentImage._360Rotation;
    }

    setCameraInitialized(true);
  }, [currentImage, cameraInitialized]);

  // Reset camera initialization when switching to a new panorama
  useEffect(() => {
    setCameraInitialized(false);
  }, [imageId]);

  // Memoized mesh rotation
  const meshRotation = useMemo(() => [0, currentImage?._360Rotation || 0, 0], [currentImage?._360Rotation]);

  // Render nothing if texture is not yet loaded (show loading state)
  if (!currentTexture) {
    return (
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />
    );
  }

  return (
    <>
      {/* Optimized OrbitControls for camera interaction */}
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />

      {/* Optimized panoramic sphere mesh */}
      <mesh
        ref={meshRef}
        rotation={meshRotation}
        scale={[1, 1, -1]}
      >
        <primitive object={sphereGeometry} />
        <primitive object={basicMaterial} />
      </mesh>
    </>
  );
}

// Export memoized component for better performance
export default memo(PanoramicSphere);